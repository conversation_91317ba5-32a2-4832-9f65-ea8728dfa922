"""
Visualization Module
Handles all plotting and visualization for trading strategy analysis.
"""

import warnings
warnings.filterwarnings("ignore")

from typing import Dict, Any, Optional
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import vectorbt as vbt

# Configure VectorBT global settings for consistent plotting
vbt.settings.set_theme("dark")
vbt.settings['plotting']['layout']["template"] = "plotly_dark"
vbt.settings['plotting']['layout']['height'] = None
vbt.settings['plotting']['layout']['width'] = None


def create_performance_plots(portfolios: Dict[str, Any], strategy_name: str) -> Dict[str, Any]:
    """Create performance plots for multiple portfolios."""
    return plot_comprehensive_analysis(portfolios, strategy_name)


def plot_comprehensive_analysis(portfolios, strategy_name: str = "Trading Strategy",
                                mc_results: Optional[Dict[str, Any]] = None,
                                wf_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Plot everything: portfolios, Monte <PERSON>, and walk-forward analysis."""
    try:
        # Validate portfolios input
        if portfolios is None:
            print("⚠️ No portfolios provided")
            return {"success": False, "error": "No portfolios provided"}

        # Convert single portfolio to dict format
        if not isinstance(portfolios, dict):
            portfolios = {"Portfolio": portfolios}

        if not portfolios:
            print("⚠️ Empty portfolios dictionary provided")
            return {"success": False, "error": "Empty portfolios dictionary"}

        # Plot portfolios
        _plot_portfolios(portfolios, strategy_name)

        # Plot analysis results if available
        if mc_results and 'error' not in mc_results:
            print("🎲 Plotting Monte Carlo analysis...")
            _plot_monte_carlo(mc_results)

        if wf_results and 'error' not in wf_results:
            print("🚶 Plotting walk-forward analysis...")
            _plot_walkforward(wf_results)

        print("✅ Comprehensive analysis completed.")
        return {"success": True}

    except Exception as e:
        print(f"⚠️ Comprehensive analysis failed: {e}")
        return {"success": False, "error": str(e)}

def _plot_portfolios(portfolios: Dict[str, Any], strategy_name: str) -> None:
    """Plot individual portfolios and comparison using VectorBT native functionality."""
    print("📊 Creating portfolio visualizations...")

    # Plot each portfolio individually
    for name, portfolio in portfolios.items():
        try:
            print(f"📈 Creating VectorBT plot for {name}...")

            # Validate portfolio before plotting
            if not _validate_portfolio_for_plotting(portfolio, name):
                continue

            fig = portfolio.plot(
                template='plotly_dark',
                height=600,
                width=1200
            )
            fig.update_layout(title=f"📊 {strategy_name} Strategy - {name} Performance")
            fig.show()
        except Exception as e:
            print(f"⚠️ Failed to plot {name}: {e}")
            # Try alternative plotting method
            _plot_portfolio_fallback(portfolio, name, strategy_name)
            continue

    # Create comparison plot if multiple portfolios
    if len(portfolios) > 1:
        _create_vectorbt_comparison(portfolios, strategy_name)


def _validate_portfolio_for_plotting(portfolio, name: str) -> bool:
    """Validate portfolio data before plotting to avoid None value errors."""
    try:
        # Check if portfolio has basic required attributes
        if not hasattr(portfolio, 'value') or not hasattr(portfolio, 'stats'):
            print(f"⚠️ Portfolio {name} missing required methods")
            return False

        # Check if portfolio has valid data
        value_series = portfolio.value()
        if value_series is None or len(value_series) == 0:
            print(f"⚠️ Portfolio {name} has no value data")
            return False

        # Check for None values in the value series
        if value_series.isna().all():
            print(f"⚠️ Portfolio {name} has all NaN values")
            return False

        # Check if portfolio has any trades
        stats = portfolio.stats()
        if stats.get('Total Trades', 0) == 0:
            print(f"⚠️ Portfolio {name} has no trades")
            return False

        return True

    except Exception as e:
        print(f"⚠️ Portfolio validation failed for {name}: {e}")
        return False


def _plot_portfolio_fallback(portfolio, name: str, strategy_name: str) -> None:
    """Fallback plotting method using basic Plotly when VectorBT plotting fails."""
    try:
        print(f"📈 Attempting fallback plot for {name}...")

        # Get portfolio value series
        value_series = portfolio.value()
        if value_series is None or len(value_series) == 0:
            print(f"⚠️ Cannot create fallback plot for {name}: no data")
            return

        # Create simple line plot
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=value_series.index,
            y=value_series.values,
            mode='lines',
            name=f'{name} Portfolio Value',
            line={'width': 2}
        ))

        fig.update_layout(
            title=f"📊 {strategy_name} Strategy - {name} Performance (Fallback)",
            xaxis_title="Date",
            yaxis_title="Portfolio Value",
            template='plotly_dark',
            height=600,
            width=1200
        )

        fig.show()
        print(f"✅ Fallback plot created for {name}")

    except Exception as e:
        print(f"⚠️ Fallback plotting also failed for {name}: {e}")


def _create_vectorbt_comparison(portfolios: Dict[str, Any], strategy_name: str):
    """Create VectorBT native comparison plot for multiple portfolios."""
    try:
        print("📊 Creating portfolio comparison...")

        if len(portfolios) <= 1:
            return

        fig = go.Figure()
        valid_portfolios = 0

        for name, portfolio in portfolios.items():
            try:
                # Validate portfolio before processing
                if not _validate_portfolio_for_plotting(portfolio, name):
                    continue

                value_series = portfolio.value()

                # Check for valid data
                if value_series is None or len(value_series) == 0 or value_series.isna().all():
                    print(f"⚠️ Skipping {name} in comparison: invalid data")
                    continue

                # Check if first value is valid for normalization
                first_value = value_series.iloc[0]
                if first_value is None or first_value == 0 or np.isnan(first_value):
                    print(f"⚠️ Skipping {name} in comparison: invalid first value")
                    continue

                # Normalize to start at 100 for fair comparison
                normalized_values = (value_series / first_value) * 100

                fig.add_trace(go.Scatter(
                    x=normalized_values.index,
                    y=normalized_values.values,
                    mode='lines',
                    name=name,
                    line={"width": 3}
                ))

                valid_portfolios += 1

            except Exception as e:
                print(f"⚠️ Failed to add {name} to comparison: {e}")
                continue

        if valid_portfolios > 1:
            fig.update_layout(
                title=f"📊 {strategy_name} Strategy - Portfolio Comparison (Normalized)",
                yaxis_title="Normalized Value (Start = 100)",
                xaxis_title="Date",
                template='plotly_dark',
                height=600,
                width=1200
            )
            fig.show()
            print(f"✅ Comparison plot created with {valid_portfolios} portfolios")
        else:
            print("⚠️ Not enough valid portfolios for comparison plot")

    except Exception as e:
        print(f"⚠️ VectorBT comparison plot failed: {e}")

def _plot_monte_carlo(mc_results: Dict[str, Any]) -> Dict[str, Any]:
    """Plot Monte Carlo results with statistical significance testing."""
    try:
        simulations = mc_results.get('simulations', [])
        statistics = mc_results.get('statistics', {})
        significance_test = mc_results.get('significance_test', {})

        if not simulations:
            return {"success": False, "reason": "no_simulations"}

        # Extract data for plotting
        returns_data = [sim['total_return'] for sim in simulations]

        if not returns_data:
            return {"success": False, "reason": "no_data"}

        # Create enhanced Monte Carlo plot with statistical significance
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=[
                'Monte Carlo Return Distribution',
                'Statistical Significance Test',
                'Percentile Analysis',
                'Performance vs Random'
            ],
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # 1. Return distribution histogram
        fig.add_trace(
            go.Histogram(
                x=returns_data,
                nbinsx=50,
                name='Random Returns',
                opacity=0.7,
                marker_color='lightblue'
            ),
            row=1, col=1
        )

        # Add actual return line if available
        actual_return = statistics.get('actual_return')
        if actual_return is not None:
            fig.add_vline(
                x=actual_return,
                line_dash="dash",
                line_color="red",
                line_width=3,
                annotation_text=f"Strategy: {actual_return:.2f}%",
                row=1, col=1
            )

        # Add confidence intervals
        p5 = statistics.get('percentile_5', 0)
        p95 = statistics.get('percentile_95', 0)
        fig.add_vrect(
            x0=p5, x1=p95,
            fillcolor="yellow", opacity=0.2,
            annotation_text="90% Confidence",
            row=1, col=1
        )

        # 2. Statistical significance visualization
        if significance_test.get('percentile_rank') is not None:
            percentile = significance_test['percentile_rank']
            is_significant = significance_test.get('is_significant', False)

            # Create significance bar chart
            fig.add_trace(
                go.Bar(
                    x=['Random', 'Strategy'],
                    y=[50, percentile],  # 50th percentile for random
                    marker_color=['lightgray', 'red' if is_significant else 'orange'],
                    name='Percentile Rank'
                ),
                row=1, col=2
            )

            # Add significance threshold lines
            fig.add_hline(y=95, line_dash="dash", line_color="green",
                         annotation_text="95th percentile", row=1, col=2)
            fig.add_hline(y=5, line_dash="dash", line_color="green",
                         annotation_text="5th percentile", row=1, col=2)

        # 3. Percentile analysis
        percentiles = [5, 10, 25, 50, 75, 90, 95]
        percentile_values = [np.percentile(returns_data, p) for p in percentiles]

        fig.add_trace(
            go.Scatter(
                x=percentiles,
                y=percentile_values,
                mode='lines+markers',
                name='Return Percentiles',
                line={'color': 'cyan', 'width': 3}
            ),
            row=2, col=1
        )

        if actual_return is not None:
            fig.add_hline(y=actual_return, line_dash="dash", line_color="red",
                         annotation_text=f"Strategy: {actual_return:.2f}%", row=2, col=1)

        # 4. Performance comparison
        mean_random = statistics.get('mean_return', 0)
        std_random = statistics.get('std_return', 1)

        comparison_data = {
            'Random Mean': mean_random,
            'Strategy': actual_return if actual_return is not None else 0,
            'Random +1σ': mean_random + std_random,
            'Random -1σ': mean_random - std_random
        }

        fig.add_trace(
            go.Bar(
                x=list(comparison_data.keys()),
                y=list(comparison_data.values()),
                marker_color=['lightgray', 'red', 'lightblue', 'lightblue'],
                name='Performance Comparison'
            ),
            row=2, col=2
        )

        # Update layout
        fig.update_layout(
            title=f"Monte Carlo Analysis - Statistical Significance: {significance_test.get('interpretation', 'Unknown')}",
            template='plotly_dark',
            height=800,
            showlegend=True
        )

        # Update axis labels
        fig.update_xaxes(title_text="Return (%)", row=1, col=1)
        fig.update_yaxes(title_text="Frequency", row=1, col=1)
        fig.update_xaxes(title_text="Category", row=1, col=2)
        fig.update_yaxes(title_text="Percentile Rank", row=1, col=2)
        fig.update_xaxes(title_text="Percentile", row=2, col=1)
        fig.update_yaxes(title_text="Return (%)", row=2, col=1)
        fig.update_xaxes(title_text="Category", row=2, col=2)
        fig.update_yaxes(title_text="Return (%)", row=2, col=2)

        fig.show()

        # Print statistical summary
        if significance_test:
            print("\n📊 Monte Carlo Statistical Analysis:")
            print(f"   Strategy Return: {actual_return:.2f}%" if actual_return else "   No strategy return available")
            print(f"   Random Mean: {mean_random:.2f}%")
            print(f"   Percentile Rank: {significance_test.get('percentile_rank', 'N/A'):.1f}%")
            print(f"   P-value: {significance_test.get('p_value', 'N/A'):.4f}")
            print(f"   Statistical Significance: {significance_test.get('interpretation', 'Unknown')}")

        return {"success": True}

    except Exception as e:
        print(f"⚠️ Monte Carlo plot failed: {e}")
        return {"success": False, "error": str(e)}

def _plot_walkforward(wf_results: Dict[str, Any]) -> Dict[str, Any]:
    """Plot enhanced walk-forward analysis results with multiple asset support."""
    try:
        if 'windows' not in wf_results:
            return {"success": False, "reason": "no_windows"}

        windows = wf_results['windows']
        if not windows:
            return {"success": False, "reason": "empty_windows"}

        # Check if we have multiple assets
        has_multiple_assets = any('asset_results' in w for w in windows)

        if has_multiple_assets:
            return _plot_multi_asset_walkforward(wf_results)
        return _plot_single_asset_walkforward(wf_results)

    except Exception as e:
        print(f"⚠️ Walk-forward plot failed: {e}")
        return {"success": False, "error": str(e)}

def _plot_single_asset_walkforward(wf_results: Dict[str, Any]) -> Dict[str, Any]:
    """Plot walk-forward analysis for single asset."""
    windows = wf_results['windows']

    # Extract data from windows
    window_nums = [w['window'] for w in windows]
    train_returns = [w['train_stats'].get('Total Return [%]', 0) for w in windows]
    test_returns = [w['test_stats'].get('Total Return [%]', 0) for w in windows]
    train_sharpes = [w['train_stats'].get('Sharpe Ratio', 0) for w in windows]
    test_sharpes = [w['test_stats'].get('Sharpe Ratio', 0) for w in windows]

    # Extract rolling Sharpe if available
    rolling_sharpe_train = []
    rolling_sharpe_test = []
    for w in windows:
        if 'rolling_sharpe_train' in w:
            rolling_sharpe_train.extend(w['rolling_sharpe_train'])
        if 'rolling_sharpe_test' in w:
            rolling_sharpe_test.extend(w['rolling_sharpe_test'])

    # Create enhanced subplots
    subplot_titles = [
        'Returns by Window (%)', 'Sharpe Ratio by Window',
        'Train vs Test Returns', 'Train vs Test Sharpe'
    ]

    if rolling_sharpe_train or rolling_sharpe_test:
        subplot_titles.extend(['Rolling Sharpe Evolution', 'Performance Degradation'])
        rows, cols = 3, 2
    else:
        rows, cols = 2, 2

    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=subplot_titles
    )

    # Plot returns and Sharpe ratios over windows
    fig.add_trace(
        go.Scatter(
            x=window_nums, y=train_returns,
            mode='lines+markers', name='Train Returns',
        ), row=1, col=1
    )
    fig.add_trace(
        go.Scatter(
            x=window_nums, y=test_returns,
            mode='lines+markers', name='Test Returns',
        ), row=1, col=1
    )

    # Sharpe over windows
    fig.add_trace(
        go.Scatter(
            x=window_nums, y=train_sharpes,
            mode='lines+markers', name='Train Sharpe',
            showlegend=False
        ), row=1, col=2
    )
    fig.add_trace(
        go.Scatter(
            x=window_nums, y=test_sharpes,
            mode='lines+markers', name='Test Sharpe',
            showlegend=False
        ), row=1, col=2
    )

    # Correlation scatter plots
    fig.add_trace(
        go.Scatter(
            x=train_returns, y=test_returns,
            mode='markers', name='Returns Correlation',
            showlegend=False
        ), row=2, col=1
    )

    fig.add_trace(
        go.Scatter(
            x=train_sharpes, y=test_sharpes,
            mode='markers', name='Sharpe Correlation',
            showlegend=False
        ), row=2, col=2
    )

    # Add diagonal reference lines
    _add_diagonal_lines(fig, train_returns, test_returns, 2, 1)
    _add_diagonal_lines(fig, train_sharpes, test_sharpes, 2, 2)

    # Add rolling Sharpe plots if available
    if (rolling_sharpe_train or rolling_sharpe_test) and rows > 2:
        if rolling_sharpe_train:
            fig.add_trace(
                go.Scatter(
                    y=rolling_sharpe_train,
                    mode='lines', name='Rolling Sharpe (Train)',
                ), row=3, col=1
            )
        if rolling_sharpe_test:
            fig.add_trace(
                go.Scatter(
                    y=rolling_sharpe_test,
                    mode='lines', name='Rolling Sharpe (Test)',
                ), row=3, col=1
            )

        # Performance degradation analysis
        degradation = [test - train for train, test in zip(train_returns, test_returns)]
        fig.add_trace(
            go.Scatter(
                x=window_nums, y=degradation,
                mode='lines+markers', name='Performance Degradation',
            ), row=3, col=2
        )
        fig.add_hline(y=0, line_dash="dash", line_color="gray", row=3, col=2)

    fig.update_layout(
        height=None,
        width=None,
        title_text="🚶 Walk-Forward Analysis - VectorBT Enhanced Performance Stability",
        template='plotly_dark',
        showlegend=True
    )
    fig.show()

    return {"success": True}

def _plot_multi_asset_walkforward(wf_results: Dict[str, Any]) -> Dict[str, Any]:
    """Plot walk-forward analysis for multiple assets."""
    windows = wf_results['windows']

    # Extract asset names from first window
    first_window = windows[0]
    asset_names = list(first_window.get('asset_results', {}).keys())

    if not asset_names:
        return _plot_single_asset_walkforward(wf_results)

    # Create comparison plots for multiple assets
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=(
            'Train Returns by Asset (%)',
            'Test Returns by Asset (%)',
            'Train vs Test Comparison',
            'Asset Performance Ranking'
        )
    )

    window_nums = [w['window'] for w in windows]

    # Define colors for assets
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

    # Plot each asset's performance
    for i, asset in enumerate(asset_names):
        color = colors[i % len(colors)]  # Cycle through colors

        # Extract asset-specific data
        train_returns = []
        test_returns = []
        for w in windows:
            asset_data = w.get('asset_results', {}).get(asset, {})
            train_returns.append(asset_data.get('train_return', 0))
            test_returns.append(asset_data.get('test_return', 0))

        # Train returns
        fig.add_trace(
            go.Scatter(
                x=window_nums, y=train_returns,
                mode='lines+markers', name=f'{asset} Train',
                line={"color": color, "width": 2}
            ), row=1, col=1
        )

        # Test returns
        fig.add_trace(
            go.Scatter(
                x=window_nums, y=test_returns,
                mode='lines+markers', name=f'{asset} Test',
                line={"color": color, "width": 2, "dash": "dash"}
            ), row=1, col=2
        )

        # Train vs Test scatter
        fig.add_trace(
            go.Scatter(
                x=train_returns, y=test_returns,
                mode='markers', name=f'{asset}',
                marker={"color": color, "size": 8},
                showlegend=False
            ), row=2, col=1
        )

    # Asset ranking analysis
    final_window = windows[-1]
    asset_performance = []
    for asset in asset_names:
        asset_data = final_window.get('asset_results', {}).get(asset, {})
        test_return = asset_data.get('test_return', 0)
        asset_performance.append((asset, test_return))

    asset_performance.sort(key=lambda x: x[1], reverse=True)
    assets_sorted, returns_sorted = zip(*asset_performance)

    fig.add_trace(
        go.Bar(
            x=list(assets_sorted), y=list(returns_sorted),
            name='Final Test Returns',
        ), row=2, col=2
    )

    fig.update_layout(
        height=None,
        width=None,
        title_text="🚶 Multi-Asset Walk-Forward Analysis - VectorBT Enhanced",
        template='plotly_dark',
        showlegend=True
    )
    fig.show()

    return {"success": True}


def _add_diagonal_lines(fig, x_data, y_data, row, col):
    """Add diagonal reference lines to scatter plots."""
    if not x_data or not y_data:
        return

    all_values = list(x_data) + list(y_data)
    min_val = min(all_values)
    max_val = max(all_values)

    fig.add_trace(
        go.Scatter(
            x=[min_val, max_val],
            y=[min_val, max_val],
            mode='lines',
            line={'dash': 'dash', 'color': 'gray'},
            name='Perfect Correlation',
            showlegend=False
        ),
        row=row, col=col
    )


def create_comparison_plot(results: Dict[str, Any], strategy_name: str) -> Dict[str, Any]:
    """Create default vs optimized comparison plot."""
    try:
        # Extract stats from both backtests
        default_stats = None
        optimized_stats = None

        if 'default_backtest' in results:
            for _, timeframes in results['default_backtest'].items():
                for _, result in timeframes.items():
                    if 'portfolio' in result:
                        default_stats = result['portfolio'].stats()
                        break
                if default_stats is not None:
                    break

        if 'full_backtest' in results:
            for _, timeframes in results['full_backtest'].items():
                for _, result in timeframes.items():
                    if 'portfolio' in result:
                        optimized_stats = result['portfolio'].stats()
                        break
                if optimized_stats is not None:
                    break

        if default_stats is None or optimized_stats is None:
            return {"success": False, "reason": "missing_stats"}

        # Create comparison chart
        metrics_names = ['Total Return (%)', 'Sharpe Ratio', 'Max Drawdown (%)', 'Win Rate (%)', 'Total Trades']
        default_values = [
            float(default_stats.get('Total Return [%]', 0)),
            float(default_stats.get('Sharpe Ratio', 0)),
            float(default_stats.get('Max Drawdown [%]', 0)),
            float(default_stats.get('Win Rate [%]', 0)),
            int(default_stats.get('Total Trades', 0))
        ]
        optimized_values = [
            float(optimized_stats.get('Total Return [%]', 0)),
            float(optimized_stats.get('Sharpe Ratio', 0)),
            float(optimized_stats.get('Max Drawdown [%]', 0)),
            float(optimized_stats.get('Win Rate [%]', 0)),
            int(optimized_stats.get('Total Trades', 0))
        ]

        fig = go.Figure()

        # Add bars for comparison
        fig.add_trace(go.Bar(
            name='Default Parameters',
            x=metrics_names,
            y=default_values,
            marker_color='lightblue',
            opacity=0.7
        ))

        fig.add_trace(go.Bar(
            name='Optimized Parameters',
            x=metrics_names,
            y=optimized_values,
            marker_color='red',
            opacity=0.7
        ))

        fig.update_layout(
            title=f'{strategy_name} Strategy: Default vs Optimized Parameters',
            xaxis_title='Metrics',
            yaxis_title='Values',
            barmode='group',
            template='plotly_dark',
            height=600
        )

        fig.show()

        # Print improvement summary
        print("\n📈 Optimization Impact Summary:")
        print(f"   Return: {default_values[0]:.2f}% → {optimized_values[0]:.2f}% ({optimized_values[0] - default_values[0]:+.2f}%)")
        print(f"   Sharpe: {default_values[1]:.3f} → {optimized_values[1]:.3f} ({optimized_values[1] - default_values[1]:+.3f})")
        print(f"   Max DD: {default_values[2]:.2f}% → {optimized_values[2]:.2f}% ({optimized_values[2] - default_values[2]:+.2f}%)")
        print(f"   Win Rate: {default_values[3]:.1f}% → {optimized_values[3]:.1f}% ({optimized_values[3] - default_values[3]:+.1f}%)")

        return {"success": True}

    except Exception as e:
        print(f"⚠️ Comparison plot failed: {e}")
        return {"success": False, "error": str(e)}
